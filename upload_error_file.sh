#!/bin/bash

# <PERSON><PERSON>t để upload file error.txt lên Jira task UBG-2792
# Sử dụng Jira REST API với multipart/form-data

echo "🚀 Uploading error.txt to Jira task UBG-2792..."

# Jira configuration (từ MCP tool hiện tại)
JIRA_BASE_URL="https://urbox.atlassian.net"
ISSUE_KEY="UBG-2792"
FILE_PATH="error.txt"

# Kiểm tra file có tồn tại không
if [ ! -f "$FILE_PATH" ]; then
    echo "❌ File $FILE_PATH không tồn tại!"
    exit 1
fi

echo "📁 File found: $FILE_PATH"
echo "🎯 Target issue: $ISSUE_KEY"
echo "🌐 Jira URL: $JIRA_BASE_URL"
echo ""

# Yêu cầu nhập email và API token
read -p "📧 Nhập email Jira của bạn: " JIRA_EMAIL
read -s -p "🔑 Nhập API token của bạn: " JIRA_API_TOKEN
echo ""
echo ""

# Tạo base64 auth string
AUTH_STRING=$(echo -n "$JIRA_EMAIL:$JIRA_API_TOKEN" | base64)

echo "🔄 Uploading file..."

# Thực hiện upload với curl
RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "Authorization: Basic $AUTH_STRING" \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@$FILE_PATH" \
  "$JIRA_BASE_URL/rest/api/3/issue/$ISSUE_KEY/attachments")

# Tách response và HTTP code
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')

echo ""
echo "📊 Response:"
echo "HTTP Code: $HTTP_CODE"
echo "Response Body: $RESPONSE_BODY"
echo ""

# Kiểm tra kết quả
if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "201" ]; then
    echo "✅ Upload thành công!"
    echo "🔗 File đã được đính kèm vào task $ISSUE_KEY"
else
    echo "❌ Upload thất bại!"
    echo "💡 Kiểm tra lại email, API token và quyền truy cập"
fi

echo ""
echo "🔗 Xem task tại: $JIRA_BASE_URL/browse/$ISSUE_KEY"
