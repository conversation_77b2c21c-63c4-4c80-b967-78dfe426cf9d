#!/usr/bin/env python3
"""
Auto Jira Upload Test
Script tự động với thông tin có sẵn
"""

import requests
import os
import base64

def auto_upload_test():
    """Test tự động upload file lên Jira"""
    
    print("🚀 Auto Jira Upload Test")
    print("=" * 50)
    
    # Thông tin có sẵn
    JIRA_URL = "https://urbox.atlassian.net"
    EMAIL = "<EMAIL>"  # Email đúng
    API_TOKEN = "ATATT3xFfGF0ecaHuny1fEOTWhegBAMFQza3mTBbmZ02ebJ9NF_Pej5oikaeHYv1I0rrG7LHtODbYT0bzM6Ow9KpSVT1eFWfR2349ToQRziVmxIzfna85LX2vrWky2LVdjxUu7-ZsIWNF0Syuj2Zg69o2YmmcAuYBbh5cv0XM0b3z7t9_KHGrVQ=B34D9C1B"
    ISSUE_KEY = "UBG-2792"
    
    print(f"🌐 Jira URL: {JIRA_URL}")
    print(f"📧 Email: {EMAIL}")
    print(f"🎯 Target Issue: {ISSUE_KEY}")
    
    # Tạo file test
    test_file = "auto_test_upload.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("🧪 Auto Test File Upload to Jira\n")
        f.write("=" * 40 + "\n")
        f.write(f"Created by: auto_test.py\n")
        f.write(f"Target issue: {ISSUE_KEY}\n")
        f.write(f"Email: {EMAIL}\n")
        f.write(f"Test purpose: Verify file upload capability\n")
        f.write("\n")
        f.write("This file tests:\n")
        f.write("✅ Basic Auth with API token\n")
        f.write("✅ Multipart file upload\n")
        f.write("✅ Jira REST API integration\n")
        f.write("✅ Error handling\n")
    
    print(f"📝 Created test file: {test_file} ({os.path.getsize(test_file)} bytes)")
    
    # Setup Basic Auth
    try:
        auth_string = f"{EMAIL}:{API_TOKEN}"
        auth_bytes = auth_string.encode('utf-8')  # Sử dụng utf-8 thay vì ascii
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            'Authorization': f'Basic {auth_b64}',
            'Accept': 'application/json',
            'X-Atlassian-Token': 'no-check'
        }
        
        print("🔐 Basic Auth setup successful")
        
    except Exception as e:
        print(f"❌ Auth setup failed: {e}")
        return False
    
    # Test 1: Connection
    print("\n" + "-" * 30)
    print("🔐 Test 1: Testing connection...")
    try:
        response = requests.get(f"{JIRA_URL}/rest/api/3/myself", headers=headers, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            user = response.json()
            print(f"✅ Connection successful!")
            print(f"👤 User: {user.get('displayName', 'Unknown')}")
            print(f"📧 Email: {user.get('emailAddress', 'Unknown')}")
            print(f"🆔 Account ID: {user.get('accountId', 'Unknown')}")
        else:
            print(f"❌ Connection failed: {response.status_code}")
            print(f"📝 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    
    # Test 2: Get Issue Info
    print("\n" + "-" * 30)
    print(f"📋 Test 2: Getting issue {ISSUE_KEY}...")
    try:
        response = requests.get(
            f"{JIRA_URL}/rest/api/3/issue/{ISSUE_KEY}",
            headers=headers,
            params={'fields': 'summary,attachment,project'},
            timeout=10
        )
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            issue = response.json()
            print(f"✅ Issue found!")
            print(f"📝 Summary: {issue['fields']['summary']}")
            print(f"🏷️ Project: {issue['fields']['project']['key']}")
            
            attachments = issue['fields'].get('attachment', [])
            print(f"📎 Current attachments: {len(attachments)}")
            
        else:
            print(f"❌ Issue not found: {response.status_code}")
            print(f"📝 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Issue error: {e}")
        return False
    
    # Test 3: Upload File
    print("\n" + "-" * 30)
    print(f"📎 Test 3: Uploading {test_file}...")
    try:
        url = f"{JIRA_URL}/rest/api/3/issue/{ISSUE_KEY}/attachments"
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            
            # Headers cho upload (bỏ Content-Type)
            upload_headers = headers.copy()
            upload_headers.pop('Content-Type', None)
            
            print(f"🔄 Uploading to: {url}")
            response = requests.post(url, headers=upload_headers, files=files, timeout=60)
            
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()[0]
                print("🎉 UPLOAD SUCCESSFUL!")
                print(f"🆔 Attachment ID: {result['id']}")
                print(f"📄 Filename: {result['filename']}")
                print(f"📏 Size: {result['size']} bytes")
                print(f"🔗 Content URL: {result['content']}")
                
                # Cleanup
                if os.path.exists(test_file):
                    os.remove(test_file)
                    print(f"🗑️ Cleaned up: {test_file}")
                
                return True
                
            else:
                print("❌ UPLOAD FAILED!")
                print(f"📝 Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False
    
    finally:
        # Cleanup file nếu còn tồn tại
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    print("🧪 Starting Automatic Jira Upload Test")
    print("=" * 60)
    
    success = auto_upload_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ File upload to Jira is working correctly!")
        print("✅ You can now upload files to Jira issues!")
    else:
        print("💥 TESTS FAILED!")
        print("❌ File upload encountered errors!")
        print("🔧 Check the error messages above for troubleshooting")
    
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
